@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
  --primary: #ff7800;
  --primary-foreground: #ffffff;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

/* Disable dark mode for now - keep light theme always */
/* @media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
    --primary: #ff7800;
    --primary-foreground: #ffffff;
  }
} */

body {
  background: #ffffff; /* Force light background */
  color: #171717; /* Force dark text */
  font-family: Arial, Helvetica, sans-serif;
}

/* CRITICAL FIX: Force dark text in all input fields */
/* Maximum specificity to override any conflicting styles */

/* Target all possible input types with highest specificity */
html input[type="text"],
html input[type="email"],
html input[type="password"],
html input[type="search"],
html input[type="url"],
html input[type="tel"],
html input[type="number"],
html input[type="date"],
html textarea,
html select,
body input[type="text"],
body input[type="email"],
body input[type="password"],
body input[type="search"],
body input[type="url"],
body input[type="tel"],
body input[type="number"],
body input[type="date"],
body textarea,
body select {
  color: #000000 !important; /* Pure black for maximum visibility */
  -webkit-text-fill-color: #000000 !important; /* For Safari/WebKit */
  text-shadow: none !important;
  opacity: 1 !important;
}

/* Target all inputs with universal selector */
* input,
* textarea,
* select {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
  text-shadow: none !important;
  opacity: 1 !important;
}

/* Force text color on all states */
input:focus,
textarea:focus,
input:active,
textarea:active,
input:hover,
textarea:hover,
input[value]:not([value=""]),
textarea[value]:not([value=""]) {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
  text-shadow: none !important;
  opacity: 1 !important;
}

/* Ensure placeholder text is visible but lighter */
input::placeholder,
textarea::placeholder {
  color: #6b7280 !important; /* Medium gray for placeholders */
  opacity: 1 !important;
  -webkit-text-fill-color: #6b7280 !important;
}

/* Override ALL possible Tailwind and other utility classes */
.text-gray-500,
.text-gray-400,
.text-gray-300,
.text-gray-200,
.text-gray-100,
.text-white,
.text-transparent,
input.text-gray-500,
input.text-gray-400,
input.text-gray-300,
textarea.text-gray-500,
textarea.text-gray-400,
textarea.text-gray-300 {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
}

/* Maximum specificity for form inputs */
html body form input,
html body form textarea,
html body form select,
html body div form input,
html body div form textarea,
html body div form select {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
  text-shadow: none !important;
  opacity: 1 !important;
}

/* Target every possible container combination */
html body div input,
html body div textarea,
html body .space-y-4 input,
html body .space-y-6 input,
html body .grid input,
html body .relative input,
html body .flex input,
html body .block input,
html body .w-full input,
html body .w-full textarea {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
  text-shadow: none !important;
  opacity: 1 !important;
}

/* Nuclear option - override everything */
[class*="input"],
[class*="text"],
[class*="form"] input,
[class*="form"] textarea {
  color: #000000 !important;
  -webkit-text-fill-color: #000000 !important;
  text-shadow: none !important;
  opacity: 1 !important;
}

/* Dark mode disabled - keeping light theme for better visibility */
/* @media (prefers-color-scheme: dark) {
  html input[type="text"],
  html input[type="email"],
  html input[type="password"],
  html input[type="search"],
  html input[type="url"],
  html input[type="tel"],
  html input[type="number"],
  html input[type="date"],
  html textarea,
  html select,
  body input,
  body textarea,
  body select,
  * input,
  * textarea,
  * select {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    background-color: #ffffff !important;
    text-shadow: none !important;
    opacity: 1 !important;
  }

  input:focus,
  textarea:focus,
  input:active,
  textarea:active,
  input:hover,
  textarea:hover {
    color: #000000 !important;
    -webkit-text-fill-color: #000000 !important;
    background-color: #ffffff !important;
    text-shadow: none !important;
    opacity: 1 !important;
  }

  input::placeholder,
  textarea::placeholder {
    color: #9ca3af !important;
    opacity: 1 !important;
    -webkit-text-fill-color: #9ca3af !important;
  }
} */
